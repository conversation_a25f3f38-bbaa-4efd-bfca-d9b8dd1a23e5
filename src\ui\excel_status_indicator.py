"""Excel operation status indicator widget."""

from PyQt6.QtCore import QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtWidgets import QWidget, QLabel, QHBoxLayout, QGraphicsOpacityEffect
from PyQt6.QtGui import QFont, QPalette
from PyQt6.QtCore import Qt


class ExcelStatusIndicator(QWidget):
    """A non-intrusive status indicator for Excel operations."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 30)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 0.8);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        # Create layout
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 5, 10, 5)
        
        # Status label
        self.status_label = QLabel("Excel operations in progress...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: transparent;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        
        # Animated dots for loading effect
        self.dots_label = QLabel("●")
        self.dots_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                background-color: transparent;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.status_label)
        layout.addStretch()
        layout.addWidget(self.dots_label)
        
        self.setLayout(layout)
        
        # Animation for dots
        self.dots_timer = QTimer()
        self.dots_timer.timeout.connect(self._animate_dots)
        self.dots_state = 0
        
        # Fade animation
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        # Auto-hide timer
        self.auto_hide_timer = QTimer()
        self.auto_hide_timer.setSingleShot(True)
        self.auto_hide_timer.timeout.connect(self.hide_indicator)
        
        # Initially hidden
        self.setVisible(False)
        self._is_showing = False
    
    def _animate_dots(self):
        """Animate the loading dots."""
        dots_patterns = ["●", "●●", "●●●", "●●", "●"]
        self.dots_label.setText(dots_patterns[self.dots_state])
        self.dots_state = (self.dots_state + 1) % len(dots_patterns)
    
    def show_indicator(self, message: str = "Excel operations in progress..."):
        """Show the indicator with optional custom message."""
        if self._is_showing:
            # Just update the message if already showing
            self.status_label.setText(message)
            return
        
        self._is_showing = True
        self.status_label.setText(message)
        
        # Stop auto-hide timer if running
        self.auto_hide_timer.stop()
        
        # Start dots animation
        self.dots_timer.start(200)
        
        # Show with fade-in animation
        self.setVisible(True)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.start()
    
    def hide_indicator(self, delay_ms: int = 0):
        """Hide the indicator with optional delay."""
        if not self._is_showing:
            return
        
        if delay_ms > 0:
            self.auto_hide_timer.start(delay_ms)
            return
        
        self._is_showing = False
        
        # Stop dots animation
        self.dots_timer.stop()
        
        # Hide with fade-out animation
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self._on_fade_out_finished)
        self.fade_animation.start()
    
    def _on_fade_out_finished(self):
        """Called when fade-out animation finishes."""
        self.setVisible(False)
        self.fade_animation.finished.disconnect()
    
    def update_message(self, message: str):
        """Update the status message."""
        if self._is_showing:
            self.status_label.setText(message)
    
    def position_in_parent(self):
        """Position the indicator in the bottom-right corner of parent."""
        if self.parent():
            parent_rect = self.parent().rect()
            x = parent_rect.width() - self.width() - 20
            y = parent_rect.height() - self.height() - 20
            self.move(x, y)
            # Ensure it's on top
            self.raise_()


class ExcelStatusManager:
    """Manager for Excel status indicators across the application."""
    
    def __init__(self):
        self.indicators = {}
        self.operation_count = 0
    
    def register_indicator(self, name: str, indicator: ExcelStatusIndicator):
        """Register a status indicator."""
        self.indicators[name] = indicator
    
    def show_operations_started(self, count: int = 1):
        """Show that Excel operations have started."""
        self.operation_count += count
        message = f"Excel operations in progress... ({self.operation_count})"
        
        for indicator in self.indicators.values():
            indicator.show_indicator(message)
    
    def show_operations_completed(self, count: int = 1):
        """Show that Excel operations have completed."""
        self.operation_count = max(0, self.operation_count - count)
        
        if self.operation_count == 0:
            # All operations complete
            for indicator in self.indicators.values():
                indicator.update_message("Excel operations completed")
                indicator.hide_indicator(delay_ms=1500)
        else:
            # Still have pending operations
            message = f"Excel operations in progress... ({self.operation_count})"
            for indicator in self.indicators.values():
                indicator.update_message(message)
    
    def hide_all(self):
        """Hide all indicators immediately."""
        self.operation_count = 0
        for indicator in self.indicators.values():
            indicator.hide_indicator()


# Global status manager instance
excel_status_manager = ExcelStatusManager()
