"""Background Excel operation queue for async processing."""

import time
import traceback
from queue import Queue, Empty
from threading import Thread, Event
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass

from PyQt6.QtCore import QObject, pyqtSignal

from .logger import get_logger
from .excel_manager import ExcelOperation


@dataclass
class OperationResult:
    """Result of an Excel operation."""
    operation_id: str
    success: bool
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0


class ExcelOperationQueue(QObject):
    """Background queue for processing Excel operations asynchronously."""
    
    # Signals
    operation_completed = pyqtSignal(str, bool, object)  # operation_id, success, result
    operation_failed = pyqtSignal(str, str, int)  # operation_id, error, retry_count
    queue_status_changed = pyqtSignal(bool)  # True if queue has pending operations
    
    def __init__(self, excel_manager):
        super().__init__()
        self.excel_manager = excel_manager
        self.operation_queue = Queue()
        self.running = True
        self.worker_thread = None
        self._stop_event = Event()
        
        # Start worker thread
        self.start_worker()
    
    def start_worker(self):
        """Start the background worker thread."""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.worker_thread = Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            logger = get_logger()
            logger.info("Excel operation queue worker started")
    
    def stop_worker(self):
        """Stop the background worker thread."""
        self.running = False
        self._stop_event.set()
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
            logger = get_logger()
            logger.info("Excel operation queue worker stopped")
    
    def add_operation(self, operation: ExcelOperation):
        """Add an operation to the queue."""
        self.operation_queue.put(operation)
        self.queue_status_changed.emit(True)
        logger = get_logger()
        logger.debug(f"Added operation to queue: {operation.operation_type} ({operation.operation_id})")
    
    def get_queue_size(self) -> int:
        """Get the current queue size."""
        return self.operation_queue.qsize()
    
    def _worker_loop(self):
        """Main worker loop for processing operations."""
        logger = get_logger()
        logger.info("Excel operation worker loop started")
        
        while self.running and not self._stop_event.is_set():
            try:
                # Get next operation with timeout
                try:
                    operation = self.operation_queue.get(timeout=1.0)
                except Empty:
                    continue
                
                # Process the operation
                result = self._process_operation(operation)
                
                # Emit appropriate signal
                if result.success:
                    self.operation_completed.emit(result.operation_id, True, result.result)
                    logger.debug(f"Operation completed: {operation.operation_type} ({result.operation_id})")

                    # Schedule cache reconciliation after successful operation
                    self.excel_manager.schedule_reconciliation(
                        operation.file_path,
                        operation.sheet_name,
                        delay_ms=1000
                    )
                else:
                    if result.retry_count < operation.max_retries:
                        # Retry the operation
                        operation.retry_count = result.retry_count + 1
                        logger.warning(f"Retrying operation {operation.operation_type} ({result.operation_id}), attempt {operation.retry_count}")
                        time.sleep(1.0)  # Brief delay before retry
                        self.operation_queue.put(operation)
                    else:
                        # Max retries reached - attempt rollback
                        rollback_success = self.excel_manager.rollback_optimistic_operation(result.operation_id)
                        if rollback_success:
                            logger.info(f"Successfully rolled back failed operation {result.operation_id}")
                        else:
                            logger.error(f"Failed to rollback operation {result.operation_id}")

                        self.operation_failed.emit(result.operation_id, result.error, result.retry_count)
                        logger.error(f"Operation failed after {result.retry_count} retries: {operation.operation_type} ({result.operation_id})")
                
                # Update queue status
                has_pending = not self.operation_queue.empty()
                self.queue_status_changed.emit(has_pending)
                
                # Mark task as done
                self.operation_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in Excel operation worker loop: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                time.sleep(1.0)  # Brief delay before continuing
        
        logger.info("Excel operation worker loop ended")
    
    def _process_operation(self, operation: ExcelOperation) -> OperationResult:
        """Process a single Excel operation."""
        logger = get_logger()
        
        try:
            if operation.operation_type == 'add_row':
                # Process add_new_row operation
                result = self.excel_manager.add_new_row(
                    operation.file_path,
                    operation.sheet_name,
                    operation.params['columns'],
                    operation.params['values']
                )
                return OperationResult(
                    operation_id=operation.operation_id,
                    success=True,
                    result=result,
                    retry_count=operation.retry_count
                )
            
            elif operation.operation_type == 'update_hyperlink':
                # Process update_pdf_link operation
                result = self.excel_manager.update_pdf_link(
                    operation.file_path,
                    operation.sheet_name,
                    operation.params['row_idx'],
                    operation.params['pdf_path'],
                    operation.params['column_name']
                )
                return OperationResult(
                    operation_id=operation.operation_id,
                    success=True,
                    result=result,
                    retry_count=operation.retry_count
                )
            
            elif operation.operation_type == 'remove_row':
                # Process remove_row operation
                result = self.excel_manager.remove_row(
                    operation.file_path,
                    operation.sheet_name,
                    operation.params['row_idx']
                )
                return OperationResult(
                    operation_id=operation.operation_id,
                    success=True,
                    result=result,
                    retry_count=operation.retry_count
                )
            
            else:
                raise ValueError(f"Unknown operation type: {operation.operation_type}")
        
        except Exception as e:
            error_msg = f"Error processing {operation.operation_type}: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            return OperationResult(
                operation_id=operation.operation_id,
                success=False,
                error=error_msg,
                retry_count=operation.retry_count
            )
    
    def clear_queue(self):
        """Clear all pending operations."""
        while not self.operation_queue.empty():
            try:
                self.operation_queue.get_nowait()
                self.operation_queue.task_done()
            except Empty:
                break
        
        self.queue_status_changed.emit(False)
        logger = get_logger()
        logger.info("Excel operation queue cleared")
