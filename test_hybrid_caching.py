#!/usr/bin/env python3
"""
Test script for the hybrid caching implementation.
This script tests the new Excel caching and optimistic update functionality.
"""

import sys
import os
import tempfile
import pandas as pd
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Simple test without UI components
def test_basic_imports():
    """Test that we can import the basic components."""
    try:
        # Test basic pandas functionality
        import pandas as pd
        print("✅ Pandas import successful")

        # Test Excel file creation
        test_data = {
            'Name': ['<PERSON>', '<PERSON>'],
            'Email': ['<EMAIL>', '<EMAIL>']
        }
        df = pd.DataFrame(test_data)
        print("✅ DataFrame creation successful")

        return True
    except Exception as e:
        print(f"❌ Basic import test failed: {e}")
        return False

def create_test_excel_file():
    """Create a test Excel file for testing."""
    test_data = {
        'Name': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Status': ['Active', 'Active', 'Inactive']
    }
    df = pd.DataFrame(test_data)
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_file.close()
    
    # Write Excel file
    df.to_excel(temp_file.name, sheet_name='Sheet1', index=False)
    return temp_file.name

def test_excel_file_creation():
    """Test Excel file creation and basic operations."""
    print("Testing Excel file creation...")

    # Create test Excel file
    excel_file = create_test_excel_file()
    print(f"Created test Excel file: {excel_file}")

    try:
        # Test reading the file back
        import pandas as pd
        df = pd.read_excel(excel_file, sheet_name='Sheet1')
        print(f"   File read successfully")
        print(f"   Data shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        print(f"   First row: {df.iloc[0].to_dict()}")

        print("\n✅ Excel file creation test completed successfully!")

    except Exception as e:
        print(f"\n❌ Excel file creation test failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up
        try:
            os.unlink(excel_file)
            print(f"\nCleaned up test file: {excel_file}")
        except Exception:
            pass

def test_operation_queue():
    """Test Excel operation queue functionality."""
    print("\n" + "="*50)
    print("Testing Excel operation queue functionality...")
    
    # Create test Excel file
    excel_file = create_test_excel_file()
    print(f"Created test Excel file: {excel_file}")
    
    try:
        # Initialize components
        excel_manager = ExcelManager()
        operation_queue = ExcelOperationQueue(excel_manager)
        
        # Load initial data
        excel_manager.load_excel_data(excel_file, 'Sheet1')
        
        # Create test operation
        operation = ExcelOperation(
            operation_type='add_row',
            operation_id='test_op_001',
            file_path=excel_file,
            sheet_name='Sheet1',
            params={
                'columns': ['Name', 'Email', 'Status'],
                'values': ['Queue Test', '<EMAIL>', 'Active']
            },
            timestamp=1234567890.0
        )
        
        print(f"\n1. Adding operation to queue...")
        operation_queue.add_operation(operation)
        print(f"   Queue size: {operation_queue.get_queue_size()}")
        
        # Wait a bit for processing
        import time
        print(f"\n2. Waiting for operation processing...")
        time.sleep(2)
        
        print(f"   Queue size after processing: {operation_queue.get_queue_size()}")
        
        # Stop the queue
        operation_queue.stop_worker()
        print(f"\n3. Operation queue stopped")
        
        print("\n✅ Operation queue tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Operation queue test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            os.unlink(excel_file)
            print(f"\nCleaned up test file: {excel_file}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 Starting Hybrid Caching Implementation Tests")
    print("="*60)
    
    # Test Excel caching
    test_excel_caching()
    
    # Test operation queue
    test_operation_queue()
    
    print("\n" + "="*60)
    print("🎉 All tests completed!")
